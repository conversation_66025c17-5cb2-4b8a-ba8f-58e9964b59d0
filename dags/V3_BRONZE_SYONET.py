"""
SYONET INTELIGENTE ETL - MIGRAÇÃO POSTGRESQL COM ESTRATÉGIA INTELIGENTE RESTAURADA

🚀 MIGRAÇÃO CORRIGIDA - INCONSISTÊNCIAS RESOLVIDAS:

✅ LÓGICA INCREMENTAL 7 DIAS RESTAURADA:
   • Implementado modo 'seven_days' na função process_table_etl()
   • Estratégia inteligente de 3 níveis: Incremental → Análise → 7 Dias/Full → Full Load
   • Análise de diferença percentual (≤10% = 7 dias, >10% = full load)

✅ SQL CUSTOMIZADO CORRIGIDO:
   • Restaurada lógica DELETE/INSERT complexa para syo_encaminhamento
   • DELETE inteligente baseado nos dados que serão inseridos
   • Suporte a chaves compostas e simples

✅ ANÁLISE INTELIGENTE RESTAURADA:
   • Decisão automática baseada em dados reais (MAX_GAP_PERCENTAGE = 10%)
   • Logs detalhados da estratégia escolhida e motivos
   • Métricas de fallback para monitoramento

✅ CONTROLE DE TIMEOUT IMPLEMENTADO:
   • Timeout de 300s para incremental 7 dias
   • Fallback automático para full load em caso de timeout
   • Logs detalhados de performance e tempo de execução

Benefícios da Migração Corrigida:
🎯 Performance otimizada com incremental 7 dias para diferenças pequenas
🔄 Fallback automático inteligente baseado em análise real
⏱️ Controle de timeout evita travamentos
📊 Logs detalhados para monitoramento e debugging
🛡️ Robustez com múltiplos níveis de fallback
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.dummy import DummyOperator
from airflow.utils.trigger_rule import TriggerRule
from airflow.exceptions import AirflowSkipException
import pymssql
import psycopg2
import pandas as pd
import logging
import threading
import time
from contextlib import contextmanager
from io import StringIO
import numpy as np
from dateutil import parser

# ===== CONFIGURAÇÕES UNIFICADAS =====

# Configurações base da DAG
BASE_DAG_ARGS = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=1),
    'trigger_rule': TriggerRule.ALL_DONE,
}

# Configuração de conexão SQL Server (origem)
SQL_SERVER_CONFIG = {
    'server': '***********',
    'user': 'bq_dwcorporativo_u', 
    'password': 'N#OK+#{Yx*',
    'database': 'master',
    'port': 50666
}

# Configuração de conexão PostgreSQL (destino)
POSTGRES_CONFIG = {
    'host': '***********',
    'user': 'bq_dwcorporativo_u',
    'password': 'N#OK+#{Yx*',
    'database': 'postgres',
    'port': 5432
}

# Configurações de tolerância para validação
MAX_ABS_DIFF = 10
CHUNK_SIZE = 50000  # Para processamento em chunks

# Configurações para Incremental 7 Dias Inteligente
MAX_GAP_PERCENTAGE = 10  # Se diferença ≤ 10%, tenta incremental 7 dias
SEVEN_DAYS_TIMEOUT = 300  # Timeout de 5 minutos (300 segundos) para incremental 7 dias

# Schema e prefixo para tabelas no PostgreSQL
POSTGRES_SCHEMA = 'dbdwcorporativo'
TABLE_PREFIX = 'bronze_syonet_'

# Filtro para carga incremental (diária) - ASPAS DUPLAS CORRETAS
DAILY_FILTER_CONDITION = """(TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
                            OR
                            TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))"""

# Filtro para carga incremental (7 dias) - ASPAS DUPLAS CORRETAS
SEVEN_DAYS_FILTER_CONDITION = """(TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days''
                                 OR
                                 TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days'')"""

# Configuração única e simples - apenas o essencial
TABLES_CONFIG = {
    # Tabelas pequenas (sempre full load)
    'syo_agenda': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_contaemail': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_contatoemail': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_clientearea': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_telefones': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'campanhav2whatsapp': {'id_field': None, 'source': 'syo_campanhav2whatsapp', 'select': None, 'custom_sql': None},
    'syo_novaclassificacao': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_novaclassificacaotipocliente': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_faixanovaclassificacao': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_etapafunil': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_empresa': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_empresausuario': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_donoconta': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_oficina': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_veiculo': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_usuario': {'id_field': None, 'source': None, 'select': 'id_usuario,nm_login,id_grupo,id_dominio,id_tipo,ap_usuario,nm_usuario,nm_sobrenome,ds_usuario,ds_email,ic_ativo,id_empresa,no_cgccpf', 'custom_sql': None},
    
    # Tabelas grandes (incremental com fallback)
    'syo_evento': {'id_field': 'id_evento', 'source': None, 'select': 'id_evento, id_pesquisa, id_contato, id_estacao, id_empresa, id_cliente, nm_cliente, id_agente, id_ramal, id_statusevento, id_situacaoevento, id_prioridadeevento, id_grupoevento, id_tipoevento, id_componente, id_grupocampanha, id_email, ds_formacontato, ds_acaoevento, dt_proximaacao, ds_iniciativacontato, ds_assunto, ds_origem, dt_horainicio, dt_horafinal, dt_limite, substring(ds_conclusao,1,3800) as ds_conclusao, dt_conclusao, ds_palavrachave, dt_previsaoresposta, dt_previsaotermino, ic_primeiroatendimento, id_dealer, id_campanha, no_versao, cd_usuarioinc, cd_usuarioalt, dt_inc, dt_alt, no_nota, ds_observacao_json, ds_prisma, ds_midia, ds_pedidocompra_json, id_agenda, no_minuta, ds_valorinvestido, ds_tempoinvestido, no_horaproximaacao, id_equipesobdemanda, no_pedido, dt_periodobase, id_modulocriacaoevento, ic_geradoautomatico, ic_principal, id_eventoprincipal, ic_tempolimiteesgotado, ds_resultado, ic_reativar, dt_classificacaofrio, id_campanhav2, ds_temperatura, uuid_evento, dt_visita, dt_venda, id_agendamentoerp, ic_centralleads, dt_previsaoentrega, id_filarecepcao, ds_resumolead, dt_visitafrotista, id_atendente_atual, id_empresa_atual, id_usuario_atual, no_os', 'custom_sql': None},
    'syo_cliente': {'id_field': 'id_cliente', 'source': None, 'select': None, 'custom_sql': None},
    'syo_acao': {'id_field': 'id_acao', 'source': None, 'select': 'id_acao, id_evento, id_cliente, id_agente, tp_acao, ds_origem, ds_iniciativa, dh_acao, ds_resultado, CAST(LEFT(ds_conclusaoacao, 1000) AS VARCHAR(3900)) as ds_conclusaoacao, id_motivoresultado, ds_descricao, ic_confirmado, ds_temperatura, id_contato, ds_pendencia, no_versao, cd_usuarioinc, cd_usuarioalt, dt_inc, dt_alt, ds_agendamento, ds_respostasmsmo, id_email, id_sms, no_latitude, no_longitude, ic_mobile', 'custom_sql': None},
    'syo_cidade': {'id_field': 'id_cidade', 'source': None, 'select': None, 'custom_sql': None},
    'syo_clientealteracao': {'id_field': 'id_clientealteracao', 'source': None, 'select': None, 'custom_sql': None},
    'syo_registrointerface': {'id_field': 'id_registrointerface', 'source': None, 'select': None, 'custom_sql': None},
    'syo_encaminhamento': {
        'id_field': 'id_encaminhamento,id_evento', # Chave composta
        'source': None,
        'select': None,
        'custom_sql': {'incremental': f"""
                SELECT *
                FROM OPENQUERY(POSTGRES,
                '
                with
                base as (
                    select distinct id_evento from public.syo_encaminhamento
                    where (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
                    OR
                    TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))
                )
                select e.* from public.syo_encaminhamento e
                inner join base b on e.id_evento = b.id_evento
                ')
        """}
    },
    'syo_clientenovaclassificacao': {'id_field': 'id_novaclassificacao', 'source': None, 'select': None, 'custom_sql': None},
    'syo_historicoetapafunilevento': {'id_field': 'id_historicoetapafunilevento', 'source': None, 'select': None, 'custom_sql': None},
    'syo_empresacliente': {
        'id_field': 'id_cliente,id_empresa',  # Chave composta
        'source': None,
        'select': None,
        'custom_sql': {
            'incremental': f"""
                SELECT *
                FROM OPENQUERY(POSTGRES, 'SELECT *
                                            FROM PUBLIC.syo_empresacliente
                                            WHERE (dt_inc is not null  OR dt_alt is not null)
                                                AND {DAILY_FILTER_CONDITION}')
            """
        }
    },
    'syo_donocontacliente': {'id_field': 'id_donocontacliente', 'source': None, 'select': None, 'custom_sql': None},
    'syo_camposregistrointerface': {'id_field': 'id_camposregistrointerface', 'source': None, 'select': 'id_camposregistrointerface, id_campointerfacenegociacao, id_registrointerface, ds_etiqueta, CAST(LEFT(ds_valor, 1000) AS VARCHAR(3900)) as ds_valor, no_versao, cd_usuarioinc, cd_usuarioalt, dt_inc, dt_alt', 'custom_sql': None},
    'syo_peca': {'id_field': 'no_controle', 'source': None, 'select': None, 'custom_sql': None},
    'syo_dadosinterfacecliente': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_interfacenegociacao': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_campointerfacenegociacao': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_modeloversao': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_motivoresultado': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    
    # Tabela customizada (SQL complexo próprio)
    'syo_evento_obs': {
        'id_field': 'id_evento',
        'source': 'syo_evento',
        'select': None,
        'custom_sql': {
            'full': """
                SELECT id_evento, dt_inc, ds_observacao
                FROM OPENQUERY(POSTGRES, 'SELECT id_evento, dt_inc, ds_observacao
                                          FROM public.syo_evento
                                          WHERE ds_observacao LIKE ''%Cadencia Meetime:%''')
            """,
            'incremental': f"""
                SELECT id_evento, dt_inc, ds_observacao
                FROM OPENQUERY(POSTGRES,
                    'SELECT id_evento, dt_inc, ds_observacao
                     from public.syo_evento
                     where {DAILY_FILTER_CONDITION}
                           and ds_observacao like ''%Cadencia Meetime:%''')
            """
        }
    }
}

@contextmanager
def get_sql_server_cursor():
    """Context manager para gerenciar conexões SQL Server"""
    conn = None
    try:
        conn = pymssql.connect(**SQL_SERVER_CONFIG)
        cursor = conn.cursor()
        yield cursor, conn
    finally:
        if conn:
            conn.close()

@contextmanager
def get_postgres_cursor():
    """Context manager para gerenciar conexões PostgreSQL"""
    conn = None
    try:
        conn = psycopg2.connect(**POSTGRES_CONFIG)
        cursor = conn.cursor()
        yield cursor, conn
    finally:
        if conn:
            conn.close()

def execute_transfer_with_timeout(extract_query, table_name, task_name, delete_conditions, is_full_load, timeout_seconds):
    """Executa transferência de dados com timeout usando threading"""
    result = {'success': False, 'error': None}
    
    def execute_transfer():
        try:
            transfer_table_data_chunked(extract_query, table_name, task_name, delete_conditions, CHUNK_SIZE, is_full_load)
            result['success'] = True
        except Exception as e:
            result['error'] = str(e)
    
    # Inicia thread da transferência
    thread = threading.Thread(target=execute_transfer)
    thread.daemon = True
    thread.start()
    
    # Aguarda conclusão ou timeout
    thread.join(timeout_seconds)
    
    if thread.is_alive():
        # Timeout ocorreu
        raise TimeoutError(f"Transferência {task_name} excedeu timeout de {timeout_seconds}s")
    
    if not result['success']:
        raise Exception(result['error'] or "Erro desconhecido na transferência")

def get_full_table_name(table_name):
    """Retorna o nome completo da tabela com schema e prefixo"""
    if table_name.startswith('tb_'):
        # Tabelas de negócio mantêm nome original com prefixo
        return f"{POSTGRES_SCHEMA}.{TABLE_PREFIX}{table_name}"
    else:
        # Tabelas syo_ recebem prefixo
        return f"{POSTGRES_SCHEMA}.{TABLE_PREFIX}{table_name}"

def create_table_schema_postgres(table_name, df, cursor):
    """Cria o schema da tabela no PostgreSQL baseado no DataFrame"""
    try:
        full_table_name = get_full_table_name(table_name)
        
        # Drop table if exists
        cursor.execute(f"DROP TABLE IF EXISTS {full_table_name}")
        
        # Mapear tipos pandas para PostgreSQL
        type_mapping = {
            'object': 'TEXT',
            'int64': 'BIGINT',
            'int32': 'INTEGER',
            'float64': 'DOUBLE PRECISION',
            'float32': 'REAL',
            'bool': 'BOOLEAN',
            'datetime64[ns]': 'TIMESTAMP'
        }
        
        # Construir CREATE TABLE
        columns = []
        for col_name, dtype in df.dtypes.items():
            pg_type = type_mapping.get(str(dtype), 'TEXT')
            # Sanitizar nome da coluna
            clean_col_name = col_name.replace(' ', '_').replace('/', '_').lower()
            columns.append(f'"{clean_col_name}" {pg_type}')
        
        create_sql = f"CREATE TABLE {full_table_name} ({', '.join(columns)})"
        cursor.execute(create_sql)
        
        print(f"✓ Tabela criada para {full_table_name}")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar tabela para {full_table_name}: {str(e)}")
        return False

def insert_data_in_chunks(df, full_table_name, pg_cursor, chunksize=CHUNK_SIZE):
    """
    Insert data into PostgreSQL in chunks with robust error handling.
    Based on proven production code.
    
    Args:
        df (DataFrame): The data to insert.
        full_table_name (str): The target table with schema.
        pg_cursor: PostgreSQL cursor.
        chunksize (int): The size of each data chunk to insert.
    """
    
    def insert_chunk(chunk, table, cursor, use_quotes=False):
        with StringIO() as sio:
            if use_quotes:
                for col in chunk.columns:
                    if chunk[col].dtype == 'object':
                        chunk.loc[:, col] = chunk[col].apply(lambda x: f'"{x}"' if pd.notnull(x) else x)
                chunk.to_csv(sio, index=None, header=None, quotechar='"', quoting=3, escapechar='\\')
            else:
                chunk.to_csv(sio, index=None, header=None)
            sio.seek(0)
            quote_option = " QUOTE '\"'" if use_quotes else ""
            cursor.copy_expert(f"COPY {table} FROM STDIN WITH CSV{quote_option}", sio)

    def insert_chunk_with_bytes_conversion(chunk, table, cursor, use_quotes=False):
        for col in chunk.columns:
            if chunk[col].dtype == 'object':
                try:
                    chunk[col] = chunk[col].apply(lambda x: x.decode('utf-8') if isinstance(x, bytes) else str(x) if x is not None else x)
                except Exception as e:
                    print(f"⚠️ Failed to convert bytes in column {col}. Error: {e}")
        insert_chunk(chunk, table, cursor, use_quotes)

    def clean_data(chunk, column_name=None, alter_column=None):
        if column_name and alter_column:
            alter_table_query = f'ALTER TABLE {full_table_name} ALTER COLUMN "{column_name}" TYPE {alter_column};'
            pg_cursor.execute(alter_table_query)
            print(f"✓ Altered column {column_name} to {alter_column} in table {full_table_name}.")
        else:
            for col in chunk.columns:
                if chunk[col].dtype == 'object':
                    chunk[col] = chunk[col].apply(lambda x: x.replace('\r', '').replace('\n', ' ') if isinstance(x, str) else x)
                    chunk[col] = chunk[col].apply(lambda x: x.replace('"', '""') if isinstance(x, str) else x)

    def infer_and_convert_dates(chunk, column_name):
        """Infere o formato da data e converte os valores da coluna para datetime."""
        # Amostra de valores não nulos da coluna
        sample_data = chunk[column_name].dropna().head(10)
        
        # Tentar identificar o formato de data com base em exemplos
        inferred_format = None
        for value in sample_data:
            try:
                # Tentar inferir o formato explicitamente
                parsed_date = parser.parse(str(value), dayfirst=True)
                inferred_format = "%d/%m/%Y" if parsed_date.strftime("%d/%m/%Y") == str(value) else "%d-%m-%Y"
                break
            except Exception:
                continue

        try:
            chunk[column_name] = pd.to_datetime(chunk[column_name], errors='coerce', format=inferred_format)
            print(f"✓ Inferred and applied date format {inferred_format} to column {column_name}.")
        except Exception as e:
            print(f"❌ Failed to apply inferred date format {inferred_format} to column {column_name}. Error: {str(e)}")
            chunk[column_name] = pd.NaT  # Configurar como valores nulos caso falhe

        # Log de diagnóstico para valores não convertidos
        null_count = chunk[column_name].isnull().sum()
        if null_count > 0:
            print(f"⚠️ Column {column_name} has {null_count} null values after conversion.")
    
    print(f"📥 Inserting data into table: {full_table_name}")
    
    for i in range(0, len(df), chunksize):
        chunk = df.iloc[i:i + chunksize].copy()  # Create a copy to avoid SettingWithCopyWarning
        chunk.columns = chunk.columns.str.lower().str.replace(' ', '_')
        attempt_insert = True
        use_quotes = False
        attempt_count = 0
        bytes_conversion_done = False
        first_clean = False

        while attempt_insert and attempt_count < 8:
            try:
                pg_cursor.execute(f"SAVEPOINT before_insert_{i}")
                
                if not first_clean:
                    clean_data(chunk)
                    first_clean = True
                    
                if bytes_conversion_done:
                    bytes_conversion_done = False
                    insert_chunk_with_bytes_conversion(chunk, full_table_name, pg_cursor, use_quotes)
                    print(f"✓ Inserted chunk with {len(chunk)} records into {full_table_name} after bytes conversion.")
                else:
                    insert_chunk(chunk, full_table_name, pg_cursor, use_quotes)
                    print(f"✓ Inserted chunk with {len(chunk)} records into {full_table_name}")
                    
                attempt_insert = False
                
            except psycopg2.errors.BadCopyFileFormat as e:
                print(f"⚠️ Error with default insert: {str(e)}. Retrying with quoted CSV.")
                pg_cursor.execute(f"ROLLBACK TO SAVEPOINT before_insert_{i}")
                use_quotes = True
                attempt_count += 1
                
            except (psycopg2.errors.StringDataRightTruncation, psycopg2.errors.InvalidDatetimeFormat) as e:
                print(f"❌ StringDataRightTruncation encountered: {str(e)}. Attempting to alter table and retry.")
                pg_cursor.execute(f"ROLLBACK TO SAVEPOINT before_insert_{i}")
                error_message = str(e).split("\n")
                column_name = None
                for line in error_message:
                    if "column" in line:
                        column_name = line.split("column ")[1].split(":")[0].strip().replace('"', '')
                        break
                if column_name:
                    pg_cursor.execute(f"ALTER TABLE {full_table_name} ALTER COLUMN \"{column_name}\" TYPE TEXT;")
                    print(f"✓ Altered column {column_name} to TEXT in table {full_table_name}. Retrying insert.")
                else:
                    print("❌ Could not identify the problematic column for StringDataRightTruncation error.")
                    raise e
                    
            except psycopg2.errors.DatetimeFieldOverflow as e:
                print(f"❌ DatetimeFieldOverflow encountered: {str(e)}. Attempting to clean column and retry.")
                pg_cursor.execute(f"ROLLBACK TO SAVEPOINT before_insert_{i}")
                error_message = str(e).split("\n")
                column_name = None
                for line in error_message:
                    if "column" in line:
                        column_name = line.split("column ")[1].split(":")[0].strip().replace('"', '')
                        break
                if column_name:
                    if chunk[column_name].dtype == 'object':
                        chunk[column_name] = chunk[column_name].str.strip()
                    infer_and_convert_dates(chunk, column_name)
                else:
                    print("❌ Could not identify the problematic column for DatetimeFieldOverflow error.")
                    raise e
                    
            except TypeError as e:
                attempt_count += 1
                if "__str__ returned non-string (type bytes)" in str(e):
                    print(f"⚠️ Encountered TypeError due to bytes. Retrying with bytes conversion.")
                    pg_cursor.execute(f"ROLLBACK TO SAVEPOINT before_insert_{i}")
                    bytes_conversion_done = True
                else:
                    print(f"❌ Failed to insert chunk with {len(chunk)} records into {full_table_name}: {str(e)}")
                    raise e
                    
            except psycopg2.errors.InvalidTextRepresentation as e:
                print(f"⚠️ InvalidTextRepresentation encountered: {str(e)}. Attempting to clean data and retry.")
                pg_cursor.execute(f"ROLLBACK TO SAVEPOINT before_insert_{i}")
                error_message = str(e).split("\n")
                column_name = None
                alter_column = None
                for line in error_message:
                    if any(word in line for word in ("integer", "smallint", "bigint")):
                        alter_column = 'double precision'
                    elif any(word in line for word in ("bytea",)):
                        alter_column = 'text'
                    if "column" in line:
                        column_name = line.split("column ")[1].split(":")[0].strip().replace('"', '')
                        break
                
                if alter_column and column_name:
                    clean_data(chunk, column_name, alter_column)
                else:
                    clean_data(chunk)
                    
            except Exception as e:
                print(f"❌ Failed to insert chunk with {len(chunk)} records into {full_table_name}: {str(e)}")
                raise e

            if attempt_count >= 8:
                print(f"❌ Failed to insert chunk with {len(chunk)} records into {full_table_name} after {attempt_count} attempts.")
                raise RuntimeError(f"Max attempts reached for inserting chunk with {len(chunk)} records into {full_table_name}")

def transfer_table_data_chunked(extract_query, table_name, task_name, delete_conditions=None, read_chunksize=50000, is_full_load=False):
    """Transfere dados do SQL Server para PostgreSQL via pandas com leitura em chunks"""
    try:
        full_table_name = get_full_table_name(table_name)
        print(f"\n===== Iniciando {task_name} ===== ({full_table_name})")
        print(f"--- SQL EXTRACT ({task_name}) ---\n{extract_query}\n--- END ---")
        
        with get_postgres_cursor() as (pg_cursor, pg_conn):
            table_created = False
            total_rows_processed = 0
            
            with get_sql_server_cursor() as (sql_cursor, sql_conn):
                # Tentar com chunks primeiro, se falhar usar leitura completa
                try:
                    print(f"🔄 Tentando extração em chunks de {read_chunksize} registros...")
                    chunk_reader = pd.read_sql(extract_query, sql_conn, chunksize=read_chunksize)
                    
                    for chunk_num, df_chunk in enumerate(chunk_reader):
                        print(f"📦 Processando chunk {chunk_num + 1}: {len(df_chunk)} registros")
                        
                        # Primeira execução: preparar tabela
                        if not table_created:
                            total_rows_processed += _prepare_table_for_load(
                                df_chunk, full_table_name, table_name, is_full_load, 
                                delete_conditions, pg_cursor, pg_conn
                            )
                            table_created = True
                        
                        # Inserir dados
                        if len(df_chunk) > 0:
                            df_chunk = df_chunk.replace({np.nan: None})
                            insert_data_in_chunks(df_chunk, full_table_name, pg_cursor, CHUNK_SIZE)
                            total_rows_processed += len(df_chunk)
                        
                        pg_conn.commit()
                    
                except Exception as chunk_error:
                    print(f"⚠️ Erro na leitura em chunks: {str(chunk_error)}")
                    print(f"🔄 Tentando leitura completa como fallback...")
                    
                    try:
                        # Fallback: leitura completa
                        df = pd.read_sql(extract_query, sql_conn)
                        print(f"📊 Extraídos {len(df)} registros (leitura completa)")
                        
                        # Preparar tabela se ainda não foi criada
                        if not table_created:
                            if len(df) > 0:
                                _prepare_table_for_load(
                                    df, full_table_name, table_name, is_full_load,
                                    delete_conditions, pg_cursor, pg_conn
                                )
                            else:
                                _handle_empty_table(
                                    extract_query, full_table_name, table_name, is_full_load,
                                    pg_cursor, pg_conn, sql_conn
                                )
                            table_created = True
                        
                        # Inserir dados
                        if len(df) > 0:
                            df = df.replace({np.nan: None})
                            insert_data_in_chunks(df, full_table_name, pg_cursor, CHUNK_SIZE)
                            total_rows_processed = len(df)
                        
                        pg_conn.commit()
                        
                    except Exception as fallback_error:
                        print(f"❌ Erro também na leitura completa: {str(fallback_error)}")
                        
                        # Último recurso: criar tabela vazia
                        if not table_created:
                            _handle_empty_table(
                                extract_query, full_table_name, table_name, is_full_load,
                                pg_cursor, pg_conn, sql_conn
                            )
                        
                        raise fallback_error
            
            print(f"✓ {task_name} concluído - {total_rows_processed} registros processados\n")
        
    except Exception as e:
        logging.error(f"Erro em {task_name}: {str(e)}")
        print(f"✗ Erro em {task_name}: {str(e)}\n")
        raise

def _prepare_table_for_load(df_chunk, full_table_name, table_name, is_full_load, delete_conditions, pg_cursor, pg_conn):
    """Prepara a tabela para carga (full ou incremental)"""
    if is_full_load:
        # FULL LOAD: sempre dropar e recriar tabela
        print(f"🔄 Full Load: Dropando e recriando tabela {full_table_name}")
        pg_cursor.execute(f"DROP TABLE IF EXISTS {full_table_name}")
        pg_conn.commit()
        
        print(f"📋 Criando schema para {full_table_name}")
        if not create_table_schema_postgres(table_name, df_chunk, pg_cursor):
            raise Exception(f"Falha ao criar schema para {full_table_name}")
        pg_conn.commit()
    else:
        # INCREMENTAL: executar DELETEs se necessário
        if delete_conditions:
            for delete_sql in delete_conditions:
                print(f"🗑️ Executando DELETE: {delete_sql}")
                try:
                    pg_cursor.execute(delete_sql)
                except psycopg2.errors.UndefinedTable:
                    print(f"⚠️ Tabela ainda não existe, DELETE ignorado")
            pg_conn.commit()
        
        # Criar schema se tabela não existir
        pg_cursor.execute(f"SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = '{POSTGRES_SCHEMA}' AND table_name = '{TABLE_PREFIX}{table_name}')")
        table_exists = pg_cursor.fetchone()[0]
        
        if not table_exists:
            print(f"📋 Criando schema para {full_table_name}")
            if not create_table_schema_postgres(table_name, df_chunk, pg_cursor):
                raise Exception(f"Falha ao criar schema para {full_table_name}")
            pg_conn.commit()
    
    return 0

def _handle_empty_table(extract_query, full_table_name, table_name, is_full_load, pg_cursor, pg_conn, sql_conn):
    """Trata casos de tabelas vazias"""
    print(f"⚠️ Tratando tabela vazia para {full_table_name}")
    
    if is_full_load:
        print(f"🔄 Full Load: Dropando tabela existente {full_table_name}")
        pg_cursor.execute(f"DROP TABLE IF EXISTS {full_table_name}")
        pg_conn.commit()
    
    print(f"🔄 Buscando estrutura da tabela para criar tabela vazia...")
    try:
        # Query modificada para obter apenas estrutura (com LIMIT 0)
        if "LIMIT 0" not in extract_query:
            structure_query = extract_query.rstrip(")'") + " LIMIT 0')"
        else:
            structure_query = extract_query
            
        df_structure = pd.read_sql(structure_query, sql_conn)
        
        if len(df_structure.columns) > 0:
            print(f"📋 Estrutura obtida: {len(df_structure.columns)} colunas")
            if not create_table_schema_postgres(table_name, df_structure, pg_cursor):
                raise Exception(f"Falha ao criar schema para {full_table_name}")
        else:
            # Criar tabela genérica
            df_generic = pd.DataFrame({'temp_column': pd.Series([], dtype='object')})
            if not create_table_schema_postgres(table_name, df_generic, pg_cursor):
                raise Exception(f"Falha ao criar schema para {full_table_name}")
        
        pg_conn.commit()
        print(f"📥 Tabela {full_table_name} criada sem registros (tabela vazia)")
                        
    except Exception as struct_e:
        print(f"⚠️ Erro ao obter estrutura ({str(struct_e)}), criando tabela genérica...")
        df_generic = pd.DataFrame({'temp_column': pd.Series([], dtype='object')})
        if not create_table_schema_postgres(table_name, df_generic, pg_cursor):
            raise Exception(f"Falha ao criar schema para {full_table_name}")
        pg_conn.commit()

def transfer_table_data(extract_query, table_name, task_name, delete_conditions=None, is_full_load=False):
    """Wrapper para manter compatibilidade - usa versão chunked"""
    return transfer_table_data_chunked(extract_query, table_name, task_name, delete_conditions, CHUNK_SIZE, is_full_load)

def transfer_table_data_with_timeout(extract_query, table_name, task_name, delete_conditions=None, timeout_seconds=300):
    """
    Executa transferência de dados com timeout usando threading
    Implementa controle de timeout para operações incremental 7 dias, evitando travamentos
    """
    result = {'success': False, 'error': None}
    start_time = time.time()

    print(f"⏱️ Iniciando transferência com timeout de {timeout_seconds}s para {task_name}")

    def execute_transfer():
        try:
            transfer_table_data_chunked(extract_query, table_name, task_name, delete_conditions, CHUNK_SIZE, False)
            result['success'] = True
        except Exception as e:
            result['error'] = str(e)

    # Inicia thread da transferência
    thread = threading.Thread(target=execute_transfer)
    thread.daemon = True
    thread.start()

    # Aguarda conclusão ou timeout
    thread.join(timeout_seconds)

    elapsed_time = time.time() - start_time

    if thread.is_alive():
        # Timeout ocorreu
        print(f"⏱️ TIMEOUT: {task_name} excedeu {timeout_seconds}s (executado por {elapsed_time:.1f}s)")
        print(f"🔄 Fallback automático será acionado para Full Load")
        raise TimeoutError(f"Transferência {task_name} excedeu timeout de {timeout_seconds}s")

    if not result['success']:
        print(f"❌ Erro na transferência {task_name} após {elapsed_time:.1f}s: {result['error']}")
        raise Exception(result['error'] or "Erro desconhecido na transferência")

    print(f"✅ Transferência {task_name} concluída em {elapsed_time:.1f}s (limite: {timeout_seconds}s)")

def build_extract_full_load_query(table_name, source_table=None, special_select=None):
    """Constrói query para extrair dados full load"""
    source = source_table if source_table else table_name
    select_clause = special_select if special_select else "*"
    
    return f"""
    SELECT {select_clause}
    FROM OPENQUERY(POSTGRES, 'SELECT {select_clause} FROM PUBLIC.{source}')
    """

def build_extract_incremental_query(table_name, special_select=None):
    """Constrói query para extrair dados incremental"""
    select_clause = special_select if special_select else "*"
    
    return f"""
    SELECT {select_clause}
    FROM OPENQUERY(POSTGRES, 'SELECT {select_clause} FROM PUBLIC.{table_name} WHERE {DAILY_FILTER_CONDITION}')
    """

def build_extract_seven_days_query(table_name, source_table=None, special_select=None):
    """
    Constrói query para extrair dados dos últimos 7 dias
    ESTRATÉGIA EFICIENTE: Muito mais rápido que gap filling em tabelas grandes
    """
    source = source_table if source_table else table_name
    select_clause = special_select if special_select else "*"
    
    return f"""
    SELECT {select_clause}
    FROM OPENQUERY(POSTGRES, 'SELECT {select_clause} FROM PUBLIC.{source} WHERE {SEVEN_DAYS_FILTER_CONDITION}')
    """

def validate_single_table(table_name, source_table=None, task_name=None):
    """
    Valida uma única tabela imediatamente após processamento
    Retorna: (is_valid, analysis_info) onde analysis_info = (should_use_seven_days, source_count, dest_count, diff_percentage)
    """
    try:
        full_table_name = get_full_table_name(table_name)
        
        # Conta registros no destino (PostgreSQL)
        with get_postgres_cursor() as (pg_cursor, pg_conn):
            pg_cursor.execute(f"SELECT COUNT(*) FROM {full_table_name}")
            result = pg_cursor.fetchone()
            dest_count = result[0] if result else 0

        # Conta registros na origem (SQL Server via OPENQUERY)
        with get_sql_server_cursor() as (sql_cursor, sql_conn):
            source = source_table if source_table else table_name
            
            # VALIDAÇÃO CUSTOMIZADA para syo_evento_obs
            if table_name == 'syo_evento_obs':
                sql_cursor.execute(f"SELECT cnt FROM OPENQUERY(POSTGRES, 'SELECT COUNT(*) AS cnt FROM PUBLIC.syo_evento WHERE ds_observacao LIKE ''%Cadencia Meetime:%''')")
            else:
                sql_cursor.execute(f"SELECT cnt FROM OPENQUERY(POSTGRES, 'SELECT COUNT(*) AS cnt FROM PUBLIC.{source}')")
            
            result = sql_cursor.fetchone()
            source_count = result[0] if result else 0

        # Calcula diferenças
        abs_diff = abs(dest_count - source_count)
        diff_percentage = (abs_diff / source_count * 100) if source_count > 0 else 0
        
        # Análise para incremental 7 dias (informação extra para fallback)
        should_use_seven_days = (
            source_count > 0 and  
            dest_count < source_count and  
            diff_percentage <= MAX_GAP_PERCENTAGE
        )
        
        analysis_info = (should_use_seven_days, source_count, dest_count, diff_percentage)
        
        task_label = task_name or table_name
        print(f"🔍 Validação imediata {task_label}: origem={source_count:,}, destino={dest_count:,}")

        # Verifica tolerâncias
        if abs_diff > MAX_ABS_DIFF:
            print(f"❌ Divergência em {table_name}: origem={source_count:,}, destino={dest_count:,}, diff={abs_diff:,} ({diff_percentage:.1f}%)")
            print(f"📊 Recomendação para fallback: {'Incremental 7 Dias' if should_use_seven_days else 'Full Load'} (limite: {MAX_GAP_PERCENTAGE}%)")
            return False, analysis_info
        
        print(f"✅ {task_label} validado com sucesso")
        return True, analysis_info
                
    except Exception as e:
        error_msg = f"Erro na validação de {table_name}: {str(e)}"
        print(f"❌ {error_msg}")
        return False, (False, 0, 0, 0)

# ===== FUNÇÕES SILVER REMOVIDAS =====
#
# As seguintes funções foram MOVIDAS para V3-SILVER-SYONET.py:
# - process_tb_oportunidades_base()
# - process_tb_im_assinatura_original()
# - process_tb_maquinas_semana_passada()
# - process_tb_maquinas_atual()
#
# 🎯 MOTIVO DA SEPARAÇÃO:
# - São transformações silver (dados processados), não bronze (dados brutos)
# - Dependem de múltiplas tabelas bronze já processadas
# - Podem ter frequências de execução diferentes
# - Facilita manutenção e debugging
# - Segue melhores práticas de data engineering (separação bronze/silver)
#
# 🚀 BENEFÍCIOS:
# - V3-BRONZE-SYONET: Foca apenas em dados brutos com estratégia inteligente
# - V3-SILVER-SYONET: Processa transformações complexas com dependências
# - Execução otimizada: Bronze → Silver em sequência
# - Monitoramento separado por camada de dados

def process_tb_oportunidades_base(**context):
    """
    Gera tb_oportunidades_base OTIMIZADA para PostgreSQL
    🚀 OTIMIZAÇÕES IMPLEMENTADAS:
    - Filtros otimizados sem conversões custosas em WHERE
    - CTEs simplificadas com índices implícitos
    - Eliminação de regex custosa
    - JOINs otimizados com condições eficientes
    """
    print("🚀 Iniciando tb_oportunidades_base OTIMIZADA para PostgreSQL")

    # ESTRATÉGIA OTIMIZADA: Filtros eficientes + CTEs simplificadas
    extract_sql = f"""
    DROP TABLE IF EXISTS dbdwcorporativo.bronze_syonet_tb_oportunidades_base;

    CREATE TABLE dbdwcorporativo.bronze_syonet_tb_oportunidades_base AS
    WITH evt_base AS (
        SELECT *
        FROM dbdwcorporativo.bronze_syonet_syo_evento
        WHERE id_tipoevento IN ('DVM LEAD','DVM OPORTUNIDADE','DVM LEAD RELACIONAMENTO','DVM RELACIONAMENTO','DVM LICITACAO')
          AND dt_inc >= 1672531200000  -- 2023-01-01 em milliseconds (otimizado)
    ),
    acao_last AS (
        SELECT DISTINCT ON (id_evento) id_evento, dt_alt, id_motivoresultado
        FROM dbdwcorporativo.bronze_syonet_syo_acao
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
        ORDER BY id_evento, id_acao DESC
    ),
    acao_first AS (
        SELECT DISTINCT ON (id_evento) id_evento, dt_alt
        FROM dbdwcorporativo.bronze_syonet_syo_acao
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
        ORDER BY id_evento, id_acao ASC
    ),
    ri_last AS (
        SELECT DISTINCT ON (id_evento) id_evento, id_registrointerface
        FROM dbdwcorporativo.bronze_syonet_syo_registrointerface
        WHERE COALESCE(ic_comentario,'0')='0'
          AND COALESCE(ic_excluido,'0')='0'
          AND COALESCE(ic_editado,'0')='0'
        ORDER BY id_evento, id_registrointerface DESC
    ),
    ri_pivot AS (
        SELECT id_registrointerface,
               MAX(CASE WHEN ds_etiqueta='_MODELO'  THEN ds_valor END) AS _MODELO,
               MAX(CASE WHEN ds_etiqueta='_MODELO2' THEN ds_valor END) AS _MODELO2,
               MAX(CASE WHEN ds_etiqueta='_MODELO3' THEN ds_valor END) AS _MODELO3,
               MAX(CASE WHEN ds_etiqueta='_MODELO4' THEN ds_valor END) AS _MODELO4,
               MAX(CASE WHEN ds_etiqueta='_MODELO5' THEN ds_valor END) AS _MODELO5,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO'  THEN ds_valor END) AS VALOR1,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 2' THEN ds_valor END) AS VALOR2,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 3' THEN ds_valor END) AS VALOR3,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 4' THEN ds_valor END) AS VALOR4,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 5' THEN ds_valor END) AS VALOR5,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE'  THEN ds_valor END) AS QTD1,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE2' THEN ds_valor END) AS QTD2,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE3' THEN ds_valor END) AS QTD3,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE4' THEN ds_valor END) AS QTD4,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE5' THEN ds_valor END) AS QTD5,
               -- OTIMIZAÇÃO: Conversão numérica otimizada sem regex custosa
               MAX(CASE WHEN ds_etiqueta='_VERSAO'  THEN
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0
                        AND ds_valor NOT LIKE '%[^0-9]%'
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO,
               MAX(CASE WHEN ds_etiqueta='_VERSAO2' THEN
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0
                        AND ds_valor NOT LIKE '%[^0-9]%'
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO2,
               MAX(CASE WHEN ds_etiqueta='_VERSAO3' THEN
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0
                        AND ds_valor NOT LIKE '%[^0-9]%'
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO3,
               MAX(CASE WHEN ds_etiqueta='_VERSAO4' THEN
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0
                        AND ds_valor NOT LIKE '%[^0-9]%'
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO4,
               MAX(CASE WHEN ds_etiqueta='_VERSAO5' THEN
                   CASE WHEN ds_valor IS NOT NULL AND LENGTH(TRIM(ds_valor)) > 0
                        AND ds_valor NOT LIKE '%[^0-9]%'
                        THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO5,
               MAX(CASE WHEN ds_etiqueta='Previsão de Faturamento' THEN ds_valor END) AS PREV_FAT,
               MAX(CASE WHEN ds_etiqueta='Data do faturamento'     THEN ds_valor END) AS DT_FAT,
               MAX(CASE WHEN ds_etiqueta='FATURA ESSE MÊS?'        THEN ds_valor END) AS FATURA_MES
        FROM dbdwcorporativo.bronze_syonet_syo_camposregistrointerface
        GROUP BY id_registrointerface
    ),
    hef_agg AS (
        SELECT id_evento,
               'MAQUINA FATURADA' AS Faturada,
               -- OTIMIZAÇÃO: Conversão de timestamp otimizada
               MIN(TO_TIMESTAMP(dt_inc/1000) - INTERVAL '3 hours') AS dt_inc
        FROM dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento
        WHERE id_etapafunil IN (6,9,17,99,20,45,153,144,154)
        GROUP BY id_evento
    )
    SELECT
        EVT.id_cliente,
        CLI.nm_cliente         AS Cliente,
        CLI.no_cpfcnpj         AS CNPJ_CPF,
        COALESCE(CLI.nm_cidadecom, CLI.nm_cidaderes) AS Cidade,
        COALESCE(CLI.sg_ufcom,    CLI.sg_ufres)      AS UF,
        EVT.id_tipoevento,
        EMP.ap_empresa         AS Filial,
        EVT.id_evento          AS Numero_Evento,
        EVT.ds_formacontato    AS Origem,
        EVT.ds_midia           AS Midia,
        EVT.ds_assunto         AS Assunto,
        -- OTIMIZAÇÃO: Conversões de timestamp otimizadas
        CASE WHEN EVT.dt_conclusao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_conclusao/1000) - INTERVAL '3 hours' END AS Data_Fechamento,
        TO_TIMESTAMP(EVT.dt_inc/1000) - INTERVAL '3 hours' AS Data_Inclusao,
        CASE WHEN al.dt_alt IS NOT NULL THEN TO_TIMESTAMP(al.dt_alt/1000) - INTERVAL '3 hours' END AS Data_U_Alteracao,
        CASE WHEN af.dt_alt IS NOT NULL THEN TO_TIMESTAMP(af.dt_alt/1000) - INTERVAL '3 hours' END AS Data_P_Alteracao,
        CASE WHEN EVT.dt_proximaacao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_proximaacao/1000) - INTERVAL '3 hours' END AS Data_P_Acao,
        COALESCE(EVT.ds_resultado,EVT.id_statusevento) AS Status,
        rp._MODELO  AS Modelo_Interesse_1,
        rp._MODELO2 AS Modelo_Interesse_2,
        rp._MODELO3 AS Modelo_Interesse_3,
        rp._MODELO4 AS Modelo_Interesse_4,
        rp._MODELO5 AS Modelo_Interesse_5,
        mv1.id_versao AS Maquina_1, mv2.id_versao AS Maquina_2, mv3.id_versao AS Maquina_3,
        mv4.id_versao AS Maquina_4, mv5.id_versao AS Maquina_5,
        rp.QTD1 AS Quantidade_1, rp.QTD2 AS Quantidade_2, rp.QTD3 AS Quantidade_3,
        rp.QTD4 AS Quantidade_4, rp.QTD5 AS Quantidade_5,
        CONCAT(ETF.no_ordem::text, '- ', ETF.nm_etapafunil) AS Etapa_Funil,
        CASE WHEN EVT.id_statusevento = 'ANDAMENTO' THEN MR.ds_motivo END AS Motivo_de_Andamento,
        CASE WHEN EVT.ds_resultado   = 'INSUCESSO'  THEN MR.ds_motivo END AS Motivo_da_Perda,
        USU.nm_login AS Usuario_AJ,
        REPLACE(rp.VALOR1,'Não informado','0,00') AS Valor_1,
        REPLACE(rp.VALOR2,'Não informado','0,00') AS Valor_2,
        REPLACE(rp.VALOR3,'Não informado','0,00') AS Valor_3,
        REPLACE(rp.VALOR4,'Não informado','0,00') AS Valor_4,
        REPLACE(rp.VALOR5,'Não informado','0,00') AS Valor_5,
        -- OTIMIZAÇÃO: Conversões de timestamp otimizadas com validação
        CASE WHEN rp.PREV_FAT IS NOT NULL AND rp.PREV_FAT ~ '^[0-9]+$'
             THEN TO_TIMESTAMP(rp.PREV_FAT::bigint/1000) - INTERVAL '3 hours' END AS Previsao_Faturamento,
        evt.ds_temperatura AS Temperatura,
        EVT.id_componente  AS Evento_Anterior,
        hef_agg.Faturada,
        hef_agg.dt_inc        AS Data_Etapa,
        CASE WHEN rp.DT_FAT IS NOT NULL AND rp.DT_FAT ~ '^[0-9]+$'
             THEN TO_TIMESTAMP(rp.DT_FAT::bigint/1000) - INTERVAL '3 hours' END AS Data_do_Faturamento,
        rp.FATURA_MES     AS Fatura_esse_mes
    FROM        evt_base EVT
    INNER JOIN  dbdwcorporativo.bronze_syonet_syo_encaminhamento ENC
               ON ENC.id_evento = EVT.id_evento AND ENC.id_statusagenteativo = 'S'
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_cliente CLI ON CLI.id_cliente = EVT.id_cliente
    INNER JOIN  dbdwcorporativo.bronze_syonet_syo_usuario USU ON USU.id_usuario = ENC.id_agente
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_empresa EMP ON EMP.id_empresa = USU.id_empresa
    LEFT JOIN   acao_last al  ON al.id_evento = EVT.id_evento
    LEFT JOIN   acao_first af ON af.id_evento = EVT.id_evento
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_motivoresultado MR ON al.id_motivoresultado = MR.id_motivoresultado
    LEFT JOIN   ri_last ril ON ril.id_evento = EVT.id_evento
    LEFT JOIN   ri_pivot rp ON rp.id_registrointerface = ril.id_registrointerface
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv1 ON mv1.id_modeloversao = rp._VERSAO
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv2 ON mv2.id_modeloversao = rp._VERSAO2
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv3 ON mv3.id_modeloversao = rp._VERSAO3
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv4 ON mv4.id_modeloversao = rp._VERSAO4
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv5 ON mv5.id_modeloversao = rp._VERSAO5
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento HEF ON EVT.id_evento = HEF.id_evento AND HEF.ic_etapaatual = '1'
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_etapafunil ETF ON HEF.id_etapafunil = ETF.id_etapafunil
    LEFT JOIN   hef_agg ON hef_agg.id_evento = EVT.id_evento
    """

    print("🚀 Executando query otimizada para tb_oportunidades_base...")
    start_time = time.time()

    try:
        with get_postgres_cursor() as (cursor, conn):
            # Configurar timeout para a sessão (10 minutos)
            cursor.execute("SET statement_timeout = '600s'")
            cursor.execute(extract_sql)
            conn.commit()

        elapsed_time = time.time() - start_time
        print(f"✅ TB_OPORTUNIDADES_BASE concluído em {elapsed_time:.1f}s")

    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"❌ Erro em tb_oportunidades_base após {elapsed_time:.1f}s: {str(e)}")
        raise
    
    # Validação - tabela de negócio, sem comparação direta com origem
    print(f"🔍 Iniciando validação imediata de tb_oportunidades_base")
    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute("SELECT COUNT(*) FROM dbdwcorporativo.bronze_syonet_tb_oportunidades_base")
            result = cursor.fetchone()
            count = result[0] if result else 0
            
            print(f"📊 tb_oportunidades_base criada com {count} registros")
            
            if count == 0:
                print(f"⚠️ AVISO: tb_oportunidades_base está vazia - verificar filtros e dependências")
            else:
                print(f"✅ tb_oportunidades_base validada com sucesso")
                
    except Exception as e:
        error_msg = f"Erro na validação de tb_oportunidades_base: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_tb_im_assinatura_original(**context):
    """
    Gera tb_IM_Assinatura_original OTIMIZADA para PostgreSQL
    🚀 OTIMIZAÇÕES IMPLEMENTADAS:
    - Filtros otimizados sem conversões custosas em WHERE
    - CTEs simplificadas com DISTINCT ON
    - Eliminação de regex custosa
    - JOINs otimizados com condições eficientes
    """
    print("🚀 Iniciando tb_IM_Assinatura_original OTIMIZADA para PostgreSQL")

    extract_sql = f"""
    DROP TABLE IF EXISTS dbdwcorporativo.bronze_syonet_tb_IM_Assinatura_original;

    CREATE TABLE dbdwcorporativo.bronze_syonet_tb_IM_Assinatura_original AS
    WITH evt_base AS (
        SELECT *
        FROM dbdwcorporativo.bronze_syonet_syo_evento
        WHERE id_tipoevento IN ('INDICACAO ASSINATURA','DVA ASSINATURA','PRECIFICACAO ASSINATURA',
                                'DVA LEAD RELACIONAMENTO','DVA RELACIONAMENTO',
                                'PRECIFICACAO AVANT','DVA ASSINATURA AVANT')
          AND id_statusevento <> 'CANCELADO'
          AND dt_inc > 1704067200000  -- 2023-12-31 em milliseconds (otimizado)
    ),
    acao_last AS (
        SELECT DISTINCT ON (id_evento) id_evento, dt_alt, id_motivoresultado
        FROM dbdwcorporativo.bronze_syonet_syo_acao
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
        ORDER BY id_evento, id_acao DESC
    ),
    acao_first AS (
        SELECT DISTINCT ON (id_evento) id_evento, dt_alt
        FROM dbdwcorporativo.bronze_syonet_syo_acao
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
        ORDER BY id_evento, id_acao ASC
    ),
    ri_last AS (
        SELECT DISTINCT ON (id_evento) id_evento, id_registrointerface
        FROM dbdwcorporativo.bronze_syonet_syo_registrointerface
        WHERE COALESCE(ic_comentario,'0')='0'
          AND COALESCE(ic_excluido,'0')='0'
          AND COALESCE(ic_editado,'0')='0'
        ORDER BY id_evento, id_registrointerface DESC
    ),
    ri_pivot AS (
        SELECT id_registrointerface,
               MAX(CASE WHEN ds_etiqueta='_MODELO'  THEN ds_valor END)  AS _MODELO,
               MAX(CASE WHEN ds_etiqueta='_MODELO2' THEN ds_valor END) AS _MODELO2,
               MAX(CASE WHEN ds_etiqueta='_MODELO3' THEN ds_valor END) AS _MODELO3,
               MAX(CASE WHEN ds_etiqueta='_MODELO4' THEN ds_valor END) AS _MODELO4,
               MAX(CASE WHEN ds_etiqueta='_MODELO5' THEN ds_valor END) AS _MODELO5,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE'  THEN ds_valor END) AS QTD1,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE2' THEN ds_valor END) AS QTD2,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE3' THEN ds_valor END) AS QTD3,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE4' THEN ds_valor END) AS QTD4,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE5' THEN ds_valor END) AS QTD5,
               MAX(CASE WHEN ds_etiqueta='_VERSAO'  THEN 
                   CASE WHEN ds_valor ~ '^[0-9]+$' THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO,
               MAX(CASE WHEN ds_etiqueta='_VERSAO2' THEN 
                   CASE WHEN ds_valor ~ '^[0-9]+$' THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO2,
               MAX(CASE WHEN ds_etiqueta='_VERSAO3' THEN 
                   CASE WHEN ds_valor ~ '^[0-9]+$' THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO3,
               MAX(CASE WHEN ds_etiqueta='_VERSAO4' THEN 
                   CASE WHEN ds_valor ~ '^[0-9]+$' THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO4,
               MAX(CASE WHEN ds_etiqueta='_VERSAO5' THEN 
                   CASE WHEN ds_valor ~ '^[0-9]+$' THEN ds_valor::bigint ELSE NULL END
               END) AS _VERSAO5,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO'  THEN ds_valor END) AS VALOR1,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO2' THEN ds_valor END) AS VALOR2,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO3' THEN ds_valor END) AS VALOR3,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO4' THEN ds_valor END) AS VALOR4,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO5' THEN ds_valor END) AS VALOR5
        FROM dbdwcorporativo.bronze_syonet_syo_camposregistrointerface
        GROUP BY id_registrointerface
    )
    SELECT
        EVT.id_cliente                               AS id_cliente,
        CLI.nm_cliente                               AS Cliente,
        CLI.no_cpfcnpj                               AS CNPJ_CPF,
        EVT.id_tipoevento                            AS Tipo_Evento,
        EMP.ap_empresa                               AS Filial,
        EVT.id_evento                                AS Numero_Evento,
        EVT.ds_formacontato                          AS Origem,
        rp._MODELO                                   AS Modelo_Interesse_1,
        mv1.id_versao                                AS Maquina_1,
        rp.QTD1                                      AS Quantidade_1,
        REPLACE(rp.VALOR1,'Não informado','0,00')    AS Valor_1,
        rp._MODELO2                                  AS Modelo_Interesse_2,
        mv2.id_versao                                AS Maquina_2,
        rp.QTD2                                      AS Quantidade_2,
        REPLACE(rp.VALOR2,'Não informado','0,00')    AS Valor_2,
        rp._MODELO3                                  AS Modelo_Interesse_3,
        mv3.id_versao                                AS Maquina_3,
        rp.QTD3                                      AS Quantidade_3,
        REPLACE(rp.VALOR3,'Não informado','0,00')    AS Valor_3,
        rp._MODELO4                                  AS Modelo_Interesse_4,
        mv4.id_versao                                AS Maquina_4,
        rp.QTD4                                      AS Quantidade_4,
        REPLACE(rp.VALOR4,'Não informado','0,00')    AS Valor_4,
        rp._MODELO5                                  AS Modelo_Interesse_5,
        mv5.id_versao                                AS Maquina_5,
        rp.QTD5                                      AS Quantidade_5,
        REPLACE(rp.VALOR5,'Não informado','0,00')    AS Valor_5,
        EVT.ds_assunto                               AS Assunto,
        -- OTIMIZAÇÃO: Conversões de timestamp otimizadas
        CASE WHEN EVT.dt_conclusao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_conclusao/1000) - INTERVAL '3 hours' END AS Data_Fechamento,
        TO_TIMESTAMP(EVT.dt_inc/1000) - INTERVAL '3 hours' AS Data_Inclusao,
        CASE WHEN al.dt_alt IS NOT NULL THEN TO_TIMESTAMP(al.dt_alt/1000) - INTERVAL '3 hours' END AS Data_U_Alteracao,
        CASE WHEN af.dt_alt IS NOT NULL THEN TO_TIMESTAMP(af.dt_alt/1000) - INTERVAL '3 hours' END AS Data_P_Alteracao,
        CASE WHEN EVT.dt_proximaacao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_proximaacao/1000) - INTERVAL '3 hours' END AS Data_P_Acao,
        COALESCE(EVT.ds_resultado,EVT.id_statusevento) AS Status,
        CONCAT(ETF.no_ordem::text, '- ', ETF.nm_etapafunil) AS Etapa_Funil,
        CASE WHEN EVT.id_statusevento='ANDAMENTO' THEN MR.ds_motivo END AS Motivo_de_Andamento,
        CASE WHEN EVT.ds_resultado='INSUCESSO' THEN MR.ds_motivo END    AS Motivo_da_Perda,
        USU.nm_login                                   AS Vendedor,
        EVT.ds_temperatura                             AS Temperatura,
        EVT1.id_tipoevento                             AS Tipo_Evento_Anterior,
        EVT1.cd_usuarioinc                             AS Indicante_Evento_Anterior
    FROM evt_base EVT
    INNER JOIN dbdwcorporativo.bronze_syonet_syo_encaminhamento ENC
            ON ENC.id_evento = EVT.id_evento AND ENC.id_statusagenteativo = 'S'
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_cliente CLI ON CLI.id_cliente = EVT.id_cliente
    INNER JOIN dbdwcorporativo.bronze_syonet_syo_empresa EMP ON EMP.id_empresa = ENC.id_empresa
    INNER JOIN dbdwcorporativo.bronze_syonet_syo_usuario USU ON USU.id_usuario = ENC.id_agente
    LEFT JOIN acao_last al  ON al.id_evento = EVT.id_evento
    LEFT JOIN acao_first af ON af.id_evento = EVT.id_evento
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_motivoresultado MR ON al.id_motivoresultado = MR.id_motivoresultado
    LEFT JOIN ri_last ril ON ril.id_evento = EVT.id_evento
    LEFT JOIN ri_pivot rp ON rp.id_registrointerface = ril.id_registrointerface
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv1 ON mv1.id_modeloversao = rp._VERSAO
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv2 ON mv2.id_modeloversao = rp._VERSAO2
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv3 ON mv3.id_modeloversao = rp._VERSAO3
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv4 ON mv4.id_modeloversao = rp._VERSAO4
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv5 ON mv5.id_modeloversao = rp._VERSAO5
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento HEF ON EVT.id_evento = HEF.id_evento AND HEF.ic_etapaatual = '1'
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_etapafunil ETF ON HEF.id_etapafunil = ETF.id_etapafunil
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_evento EVT1 ON EVT1.id_evento = EVT.id_componente
    """

    print("🚀 Executando query otimizada para tb_IM_Assinatura_original...")
    start_time = time.time()

    try:
        with get_postgres_cursor() as (cursor, conn):
            # Configurar timeout para a sessão (10 minutos)
            cursor.execute("SET statement_timeout = '600s'")
            cursor.execute(extract_sql)
            conn.commit()

        elapsed_time = time.time() - start_time
        print(f"✅ TB_IM_ASSINATURA_ORIGINAL concluído em {elapsed_time:.1f}s")

    except Exception as e:
        elapsed_time = time.time() - start_time
        print(f"❌ Erro em tb_IM_Assinatura_original após {elapsed_time:.1f}s: {str(e)}")
        raise
    
    # Validação - tabela de negócio, sem comparação direta com origem
    print(f"🔍 Iniciando validação imediata de tb_IM_Assinatura_original")
    try:
        with get_postgres_cursor() as (cursor, conn):
            cursor.execute("SELECT COUNT(*) FROM dbdwcorporativo.bronze_syonet_tb_IM_Assinatura_original")
            result = cursor.fetchone()
            count = result[0] if result else 0
            
            print(f"📊 tb_IM_Assinatura_original criada com {count} registros")
            
            if count == 0:
                print(f"⚠️ AVISO: tb_IM_Assinatura_original está vazia - verificar filtros e dependências")
            else:
                print(f"✅ tb_IM_Assinatura_original validada com sucesso")
                
    except Exception as e:
        error_msg = f"Erro na validação de tb_IM_Assinatura_original: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_tb_maquinas_semana_passada(**context):
    """Processa tb_maquinas_semana_passada APENAS nas sextas-feiras"""
    if not datetime.now().weekday() == 4: # 4 = sexta-feira
        print("✓ Não é sexta-feira - pulando tb_maquinas_semana_passada")
        return
        
    print("🗓️ SEXTA-FEIRA - Processando tb_maquinas_semana_passada")
    
    # Copiar dados de tb_oportunidades_base
    try:
        full_table_name = get_full_table_name("tb_maquinas_semana_passada")
        base_table_name = get_full_table_name("tb_oportunidades_base")
        
        with get_postgres_cursor() as (pg_cursor, pg_conn):            
            # Drop and recreate
            pg_cursor.execute(f"DROP TABLE IF EXISTS {full_table_name}")
            pg_cursor.execute(f"""
                CREATE TABLE {full_table_name} AS 
                SELECT * FROM {base_table_name}
            """)
            pg_conn.commit()
            
            # Validação
            pg_cursor.execute(f"SELECT COUNT(*) FROM {full_table_name}")
            week_count = pg_cursor.fetchone()[0]
            
            pg_cursor.execute(f"SELECT COUNT(*) FROM {base_table_name}")
            base_count = pg_cursor.fetchone()[0]
            
            print(f"📊 tb_maquinas_semana_passada: {week_count} registros (base: {base_count})")
            
            if week_count != base_count:
                print(f"⚠️ AVISO: Diferença entre semana passada ({week_count}) e base ({base_count})")
            else:
                print(f"✅ tb_maquinas_semana_passada validada com sucesso")
                
    except Exception as e:
        error_msg = f"Erro na validação de tb_maquinas_semana_passada: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_tb_maquinas_atual(**context):
    """Processa tb_maquinas_atual - executa sempre"""
    print("📊 Processando tb_maquinas_atual")
    
    try:
        with get_postgres_cursor() as (pg_cursor, pg_conn):
            # Drop and recreate
            pg_cursor.execute("DROP TABLE IF EXISTS tb_maquinas_atual")
            pg_cursor.execute("""
                CREATE TABLE tb_maquinas_atual AS 
                SELECT * FROM tb_oportunidades_base
            """)
            pg_conn.commit()
            
            # Validação
            pg_cursor.execute("SELECT COUNT(*) FROM tb_maquinas_atual")
            atual_count = pg_cursor.fetchone()[0]
            
            pg_cursor.execute("SELECT COUNT(*) FROM tb_oportunidades_base")
            base_count = pg_cursor.fetchone()[0]
            
            print(f"📊 tb_maquinas_atual: {atual_count} registros (base: {base_count})")
            
            if atual_count != base_count:
                error_msg = f"Divergência entre atual ({atual_count}) e base ({base_count})"
                print(f"❌ {error_msg}")
                raise ValueError(error_msg)
            else:
                print(f"✅ tb_maquinas_atual validada com sucesso")
                
    except Exception as e:
        error_msg = f"Erro na validação de tb_maquinas_atual: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_table_etl(table_name, load_mode, id_field=None, source_table=None, special_select=None, custom_sql=None, **context):
    """
    Processa ETL de uma tabela com o modo especificado
    """
    try:
        source_table = source_table or table_name
        
        # ESTRATÉGIA 1: SQL Customizado (para tabelas especiais)
        if custom_sql:
            print(f"   🔄 Executando SQL customizado para {table_name}")
            
            # Tenta usar SQL customizado para o modo solicitado
            if load_mode == 'full' and 'full' in custom_sql:
                extract_query = custom_sql['full']
                transfer_table_data(extract_query, table_name, f"{table_name.upper()}_CUSTOM_FULL", is_full_load=True)
                success, analysis_info = validate_single_table(table_name, source_table, f"{table_name.upper()}_CUSTOM_FULL")
                return success, analysis_info
                
            elif load_mode == 'incremental' and 'incremental' in custom_sql:
                extract_query = custom_sql['incremental']

                # Para incremental customizado, preparar DELETE inteligente antes da inserção
                delete_conditions = []

                # ESTRATÉGIA INTELIGENTE: DELETE baseado nos dados que serão inseridos
                print(f"   🗑️ Preparando DELETE inteligente para {table_name}")

                if table_name == 'syo_encaminhamento':
                    # Para syo_encaminhamento: DELETE baseado nos id_evento que serão atualizados
                    with get_sql_server_cursor() as (sql_cursor, sql_conn):
                        # Buscar id_evento que serão atualizados (baseado no filtro incremental)
                        check_query = f"""
                        SELECT DISTINCT id_evento
                        FROM OPENQUERY(POSTGRES, 'SELECT DISTINCT id_evento FROM PUBLIC.syo_encaminhamento
                                                  WHERE (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
                                                  OR TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))')
                        """
                        ids_df = pd.read_sql(check_query, sql_conn)
                        if not ids_df.empty:
                            full_table_name = get_full_table_name(table_name)
                            ids_list = "','".join(map(str, ids_df['id_evento'].tolist()))
                            delete_conditions.append(f"DELETE FROM {full_table_name} WHERE id_evento IN ('{ids_list}')")
                            print(f"   🗑️ DELETE preparado para {len(ids_df)} eventos em {table_name}")

                elif id_field and ',' in id_field:
                    # Chave composta - usar concatenação para DELETE
                    id_fields = [field.strip() for field in id_field.split(',')]
                    full_table_name = get_full_table_name(table_name)

                    # Construir concatenação para PostgreSQL (destino)
                    pg_concat = "||'-'||".join([f"{field}::text" for field in id_fields])

                    with get_sql_server_cursor() as (sql_cursor, sql_conn):
                        # Buscar chaves compostas que serão atualizadas
                        dash_separator = " + '-' + "
                        check_query = f"""
                        SELECT {dash_separator.join([f"CAST({field} AS VARCHAR(50))" for field in id_fields])} as composite_key
                        FROM OPENQUERY(POSTGRES, 'SELECT {id_field} FROM PUBLIC.{table_name} WHERE {DAILY_FILTER_CONDITION}')
                        """
                        keys_df = pd.read_sql(check_query, sql_conn)
                        if not keys_df.empty:
                            keys_list = "','".join(keys_df['composite_key'].tolist())
                            delete_conditions.append(f"DELETE FROM {full_table_name} WHERE {pg_concat} IN ('{keys_list}')")
                            print(f"   🗑️ DELETE preparado para {len(keys_df)} chaves compostas em {table_name}")

                elif id_field:
                    # Para outras tabelas incrementais, usar DELETE baseado no ID
                    with get_sql_server_cursor() as (sql_cursor, sql_conn):
                        # Buscar IDs que serão atualizados
                        # Para syo_evento_obs, usar tabela origem syo_evento
                        source_table_for_query = source_table if source_table else table_name
                        check_query = f"""
                        SELECT {id_field}
                        FROM OPENQUERY(POSTGRES, 'SELECT {id_field} FROM PUBLIC.{source_table_for_query} WHERE {DAILY_FILTER_CONDITION}')
                        """
                        ids_df = pd.read_sql(check_query, sql_conn)
                        if not ids_df.empty:
                            full_table_name = get_full_table_name(table_name)
                            ids_list = "','".join(map(str, ids_df[id_field].tolist()))
                            delete_conditions.append(f"DELETE FROM {full_table_name} WHERE {id_field} IN ('{ids_list}')")
                            print(f"   🗑️ DELETE preparado para {len(ids_df)} IDs em {table_name}")

                transfer_table_data(extract_query, table_name, f"{table_name.upper()}_CUSTOM_INCREMENTAL", delete_conditions)
                success, analysis_info = validate_single_table(table_name, source_table, f"{table_name.upper()}_CUSTOM_INCREMENTAL")
                return success, analysis_info
                
            else:
                # Fallback para SQL padrão se não tiver o modo customizado
                print(f"   ⚠️ SQL customizado não disponível para modo {load_mode}, usando SQL padrão")
                 
        # ESTRATÉGIA 2: SQL Padrão (tabelas normais)
        if load_mode == 'full':
            print(f"   🔄 Executando Full Load para {table_name}")
            extract_query = build_extract_full_load_query(table_name, source_table, special_select)
            transfer_table_data(extract_query, table_name, f"{table_name.upper()}_FULL", is_full_load=True)
            success, analysis_info = validate_single_table(table_name, source_table, f"{table_name.upper()}_FULL")
            return success, analysis_info
            
        elif load_mode == 'incremental':
            print(f"   🔄 Executando Incremental para {table_name}")
            extract_query = build_extract_incremental_query(table_name, special_select)

            # Preparar DELETE para incremental padrão
            delete_conditions = []
            if id_field and ',' in id_field:
                # Chave composta
                id_fields = [field.strip() for field in id_field.split(',')]
                full_table_name = get_full_table_name(table_name)

                # Construir concatenação para PostgreSQL (destino)
                pg_concat = "||'-'||".join([f"{field}::text" for field in id_fields])

                with get_sql_server_cursor() as (sql_cursor, sql_conn):
                    # Buscar chaves compostas que serão atualizadas
                    dash_separator = " + '-' + "
                    check_query = f"""
                    SELECT {dash_separator.join([f"CAST({field} AS VARCHAR(50))" for field in id_fields])} as composite_key
                    FROM OPENQUERY(POSTGRES, 'SELECT {id_field} FROM PUBLIC.{table_name} WHERE {DAILY_FILTER_CONDITION}')
                    """
                    keys_df = pd.read_sql(check_query, sql_conn)
                    if not keys_df.empty:
                        keys_list = "','".join(keys_df['composite_key'].tolist())
                        delete_conditions.append(f"DELETE FROM {full_table_name} WHERE {pg_concat} IN ('{keys_list}')")

            elif id_field:
                # Chave simples
                with get_sql_server_cursor() as (sql_cursor, sql_conn):
                    # Buscar IDs que serão atualizados
                    check_query = f"""
                    SELECT {id_field}
                    FROM OPENQUERY(POSTGRES, 'SELECT {id_field} FROM PUBLIC.{table_name} WHERE {DAILY_FILTER_CONDITION}')
                    """
                    ids_df = pd.read_sql(check_query, sql_conn)
                    if not ids_df.empty:
                        full_table_name = get_full_table_name(table_name)
                        ids_list = "','".join(map(str, ids_df[id_field].tolist()))
                        delete_conditions.append(f"DELETE FROM {full_table_name} WHERE {id_field} IN ('{ids_list}')")

            transfer_table_data(extract_query, table_name, f"{table_name.upper()}_INCREMENTAL", delete_conditions)
            success, analysis_info = validate_single_table(table_name, source_table, f"{table_name.upper()}_INCREMENTAL")
            return success, analysis_info

        elif load_mode == 'seven_days':
            print(f"   🔄 Executando Incremental 7 Dias para {table_name}")
            extract_query = build_extract_seven_days_query(table_name, source_table, special_select)

            # Preparar DELETE para incremental 7 dias
            delete_conditions = []
            if id_field and ',' in id_field:
                # Chave composta
                id_fields = [field.strip() for field in id_field.split(',')]
                full_table_name = get_full_table_name(table_name)

                # Construir concatenação para PostgreSQL (destino)
                pg_concat = "||'-'||".join([f"{field}::text" for field in id_fields])

                with get_sql_server_cursor() as (sql_cursor, sql_conn):
                    # Buscar chaves compostas que serão atualizadas (7 dias)
                    dash_separator = " + '-' + "
                    check_query = f"""
                    SELECT {dash_separator.join([f"CAST({field} AS VARCHAR(50))" for field in id_fields])} as composite_key
                    FROM OPENQUERY(POSTGRES, 'SELECT {id_field} FROM PUBLIC.{source_table} WHERE {SEVEN_DAYS_FILTER_CONDITION}')
                    """
                    keys_df = pd.read_sql(check_query, sql_conn)
                    if not keys_df.empty:
                        keys_list = "','".join(keys_df['composite_key'].tolist())
                        delete_conditions.append(f"DELETE FROM {full_table_name} WHERE {pg_concat} IN ('{keys_list}')")

            elif id_field:
                # Chave simples
                with get_sql_server_cursor() as (sql_cursor, sql_conn):
                    # Buscar IDs que serão atualizados (7 dias)
                    source_for_query = source_table if source_table else table_name
                    check_query = f"""
                    SELECT {id_field}
                    FROM OPENQUERY(POSTGRES, 'SELECT {id_field} FROM PUBLIC.{source_for_query} WHERE {SEVEN_DAYS_FILTER_CONDITION}')
                    """
                    ids_df = pd.read_sql(check_query, sql_conn)
                    if not ids_df.empty:
                        full_table_name = get_full_table_name(table_name)
                        ids_list = "','".join(map(str, ids_df[id_field].tolist()))
                        delete_conditions.append(f"DELETE FROM {full_table_name} WHERE {id_field} IN ('{ids_list}')")

            try:
                transfer_table_data_with_timeout(extract_query, table_name, f"{table_name.upper()}_SEVEN_DAYS", delete_conditions, SEVEN_DAYS_TIMEOUT)
                success, analysis_info = validate_single_table(table_name, source_table, f"{table_name.upper()}_SEVEN_DAYS")
                return success, analysis_info
            except TimeoutError as te:
                print(f"   ⏱️ Incremental 7 Dias timeout para {table_name} - será feito Full Load")
                return False, (False, 0, 0, 100)  # Força fallback para full load

        else:
            raise ValueError(f"Modo de carga inválido: {load_mode}")
            
    except Exception as e:
        print(f"   ❌ Erro no processamento {load_mode} de {table_name}: {str(e)}")
        return False, (False, 0, 0, 0)

def log_fallback_metrics(table_name, attempted_strategy, final_strategy, **context):
    """Log das métricas de fallback para monitoramento e análise de performance"""
    print(f"📊 FALLBACK METRICS: {table_name}")
    print(f"   🎯 Estratégia Tentada: {attempted_strategy}")
    print(f"   ✅ Estratégia Executada: {final_strategy}")

    if attempted_strategy != final_strategy:
        print(f"   ⚠️ FALLBACK NECESSÁRIO: {attempted_strategy} → {final_strategy}")

        # Log detalhado do motivo do fallback
        if final_strategy == 'seven_days':
            print(f"   📈 Motivo: Diferença ≤ {MAX_GAP_PERCENTAGE}% - Incremental 7 dias mais eficiente")
        elif final_strategy in ['full', 'full_custom']:
            print(f"   📊 Motivo: Diferença > {MAX_GAP_PERCENTAGE}% ou timeout - Full Load necessário")

        print(f"   🚀 Benefício: Otimização automática baseada em análise de dados reais")
    else:
        print(f"   ✓ ESTRATÉGIA ORIGINAL BEM-SUCEDIDA - Sem fallback necessário")

    print(f"   ⏱️ Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def process_table_with_smart_fallback(table_name, id_field=None, source_table=None, special_select=None, **context):
    """
    Processa tabela com fallback automático:
    1. Verifica configuração da tabela
    2. Tenta incremental (se possível)
    3. Valida resultado
    4. Se falhar na validação -> executa full load
    5. Valida novamente
    """
    
    # Pega configuração da tabela
    table_config = TABLES_CONFIG.get(table_name, {})
    
    # Parâmetros da tabela (prioriza parâmetros passados, depois config)
    final_id_field = id_field or table_config.get('id_field')
    final_source_table = source_table or table_config.get('source') or table_name
    final_special_select = special_select or table_config.get('select')
    final_custom_sql = table_config.get('custom_sql')
    incremental_failed = False
    incremental_custom_failed = False
    
    print(f"🚀 SMART FALLBACK: {table_name}")
    print(f"   📊 Configuração: {'SQL Customizado' if final_custom_sql else 'Incremental + Fallback'}")
    print(f"   🔑 ID Field: {final_id_field}")
    print(f"   📂 Source: {final_source_table}")
    print(f"   🔍 Select: {'Customizado' if final_special_select else 'Padrão'}")
    
    # ESTRATÉGIA 1: Tabelas com SQL customizado - Fallback com análise inteligente
    if final_custom_sql:
        print(f"   ⚡ Estratégia: SQL Customizado → Análise → 7 Dias/Full → Full Load")

        # NÍVEL 1: Tenta incremental customizado primeiro (se disponível)
        if 'incremental' in final_custom_sql and final_id_field:
            print(f"   🔄 NÍVEL 1: Tentando incremental customizado (diário)...")
            success, analysis_info = process_table_etl(
                table_name=table_name,
                load_mode='incremental',
                id_field=final_id_field,
                source_table=final_source_table,
                special_select=final_special_select,
                custom_sql=final_custom_sql,
                **context
            )

            if success:
                print(f"   ✅ Incremental customizado executado com sucesso!")
                log_fallback_metrics(table_name, 'incremental_custom', 'incremental_custom', **context)
                return True
            else:
                incremental_custom_failed = True
                print(f"   ⚠️ Incremental customizado falhou, analisando estratégia...")

                # NÍVEL 2: Usa dados da validação para decidir Incremental 7 Dias vs Full Load
                should_use_seven_days, source_count, dest_count, diff_percentage = analysis_info
                print(f"   📊 Análise: origem={source_count:,}, destino={dest_count:,}, diferença={diff_percentage:.1f}%")

                if should_use_seven_days:
                    # NÍVEL 2A: Incremental 7 Dias padrão (diferença ≤ 10%)
                    print(f"   🎯 Incremental 7 Dias: diferença {diff_percentage:.1f}% ≤ {MAX_GAP_PERCENTAGE}% (apenas {source_count - dest_count:,} registros)")
                    success, _ = process_table_etl(
                        table_name=table_name,
                        load_mode='seven_days',
                        id_field=final_id_field,
                        source_table=final_source_table,
                        special_select=final_special_select,
                        custom_sql=None,  # Usa SQL padrão para 7 dias
                        **context
                    )

                    if success:
                        print(f"   ✅ Incremental 7 Dias executado com sucesso!")
                        log_fallback_metrics(table_name, 'incremental_custom', 'seven_days', **context)
                        raise AirflowSkipException(f"Incremental customizado falhou para {table_name}, resolvido com incremental 7 dias")
                    else:
                        print(f"   ⚠️ Incremental 7 Dias falhou, iniciando full load...")
                else:
                    # NÍVEL 2B: Full Load customizado direto (diferença > 10%)
                    print(f"   🔄 Full Load customizado direto: diferença {diff_percentage:.1f}% > {MAX_GAP_PERCENTAGE}% (muitos registros)")
        else:
            print(f"   ⚠️ Sem incremental customizado disponível, pulando para full load...")
        
        # NÍVEL 3: Fallback final para full load customizado
        print(f"   🔄 NÍVEL 3: Executando Full Load customizado...")
        success, _ = process_table_etl(
            table_name=table_name,
            load_mode='full',
            id_field=final_id_field,
            source_table=final_source_table,
            special_select=final_special_select,
            custom_sql=final_custom_sql,
            **context
        )

        if success:
            print(f"   ✅ Full Load customizado executado com sucesso!")
            if incremental_custom_failed:
                log_fallback_metrics(table_name, 'incremental_custom', 'full_custom', **context)
                raise AirflowSkipException(f"Incremental customizado e incremental 7 dias falharam para {table_name}")
            else:
                log_fallback_metrics(table_name, 'full_custom', 'full_custom', **context)
                return True
        else:
            print(f"   ❌ Full Load customizado também falhou - erro crítico")
            raise Exception(f"Todos os níveis (incremental customizado, incremental 7 dias e full customizado) falharam para {table_name}")
    
    # ESTRATÉGIA 2: Tabelas normais -> incremental com análise inteligente
    else:
        print(f"   ⚡ Estratégia: Incremental → Análise → 7 Dias/Full → Full Load")

        # NÍVEL 1: Tenta incremental primeiro
        if final_id_field:
            print(f"   🔄 NÍVEL 1: Tentando incremental (diário)...")
            success, analysis_info = process_table_etl(
                table_name=table_name,
                load_mode='incremental',
                id_field=final_id_field,
                source_table=final_source_table,
                special_select=final_special_select,
                custom_sql=None,
                **context
            )

            if success:
                print(f"   ✅ Incremental executado com sucesso!")
                log_fallback_metrics(table_name, 'incremental', 'incremental', **context)
                return True
            else:
                incremental_failed = True
                print(f"   ⚠️ Incremental falhou, analisando estratégia...")

                # NÍVEL 2: Usa dados da validação para decidir Incremental 7 Dias vs Full Load
                should_use_seven_days, source_count, dest_count, diff_percentage = analysis_info
                print(f"   📊 Análise: origem={source_count:,}, destino={dest_count:,}, diferença={diff_percentage:.1f}%")

                if should_use_seven_days:
                    # NÍVEL 2A: Incremental 7 Dias (diferença ≤ 10%)
                    print(f"   🎯 Incremental 7 Dias: diferença {diff_percentage:.1f}% ≤ {MAX_GAP_PERCENTAGE}% (apenas {source_count - dest_count:,} registros)")
                    success, _ = process_table_etl(
                        table_name=table_name,
                        load_mode='seven_days',
                        id_field=final_id_field,
                        source_table=final_source_table,
                        special_select=final_special_select,
                        custom_sql=None,
                        **context
                    )

                    if success:
                        print(f"   ✅ Incremental 7 Dias executado com sucesso!")
                        log_fallback_metrics(table_name, 'incremental', 'seven_days', **context)
                        raise AirflowSkipException(f"Incremental falhou para {table_name}, resolvido com incremental 7 dias")
                    else:
                        print(f"   ⚠️ Incremental 7 Dias falhou, iniciando full load...")
                else:
                    # NÍVEL 2B: Full Load direto (diferença > 10%)
                    print(f"   🔄 Full Load direto: diferença {diff_percentage:.1f}% > {MAX_GAP_PERCENTAGE}% (muitos registros)")
        else:
            print(f"   ⚠️ Sem ID field para incremental, pulando para full load...")
        
        # NÍVEL 3: Fallback final para full load
        print(f"   🔄 NÍVEL 3: Executando Full Load...")
        success, _ = process_table_etl(
            table_name=table_name,
            load_mode='full',
            id_field=final_id_field,
            source_table=final_source_table,
            special_select=final_special_select,
            custom_sql=None,
            **context
        )

        if success:
            print(f"   ✅ Full Load executado com sucesso!")
            if incremental_failed:
                log_fallback_metrics(table_name, 'incremental', 'full', **context)
                raise AirflowSkipException(f"Incremental e incremental 7 dias falharam para {table_name}")
            else:
                log_fallback_metrics(table_name, 'full', 'full', **context)
                return True
        else:
            print(f"   ❌ Full Load também falhou - erro crítico")
            raise Exception(f"Todos os níveis (incremental, incremental 7 dias e full) falharam para {table_name}")

def create_smart_unified_dag():
    """
    Cria a DAG única inteligente com fallback automático para PostgreSQL
    """
    
    # ===== CONFIGURAÇÃO DA DAG =====
    
    dag = DAG(
        dag_id='V3-BRONZE-SYONET',
        description='🥉 BRONZE SYONET: Dados Brutos (syo_* tables) com Estratégia Inteligente 3 Níveis (Incremental → Análise → 7 Dias/Full → Full Load) + Timeout e Fallback Automático',
        schedule_interval=None,  # Executada apenas via orquestração
        start_date=datetime(2024, 1, 1),
        catchup=False,
        max_active_runs=1,
        max_active_tasks=5,
        default_args=BASE_DAG_ARGS
    )
    
    # ===== TASK INICIAL =====
    
    start_task = DummyOperator(
        task_id='start_smart_etl',
        dag=dag
    )
    
    # ===== GERAÇÃO DINÂMICA DE TASKS =====
    
    # Dicionário para armazenar as tasks criadas
    tasks = {}
    
    # Criar tasks para cada tabela na configuração
    for table_name, config in TABLES_CONFIG.items():
        task_id = f'smart_process_{table_name}'
        
        tasks[table_name] = PythonOperator(
            task_id=task_id,
            python_callable=process_table_with_smart_fallback,
            op_kwargs={
                'table_name': table_name,
                'id_field': config.get('id_field'),
                'source_table': config.get('source'),
                'special_select': config.get('select')
            },
            dag=dag
        )
    
    # ===== CONFIGURAÇÃO DE DEPENDÊNCIAS =====
    
    # Tabelas independentes (primeira onda)
    independent_tables = [
        'syo_agenda', 'syo_contaemail', 'syo_contatoemail', 'syo_clientearea', 
        'syo_telefones', 'campanhav2whatsapp', 'syo_novaclassificacao',
        'syo_novaclassificacaotipocliente', 'syo_faixanovaclassificacao',
        'syo_etapafunil', 'syo_empresa', 'syo_empresausuario', 'syo_donoconta',
        'syo_oficina', 'syo_usuario', 'syo_veiculo', 'syo_dadosinterfacecliente',
        'syo_peca', 'syo_evento', 'syo_cliente', 'syo_interfacenegociacao',
        'syo_modeloversao', 'syo_motivoresultado'
    ]
    
    # Configurar dependências básicas
    for table_name in independent_tables:
        if table_name in tasks:
            start_task >> tasks[table_name]
    
    # Dependências específicas (segunda onda)
    dependencies = {
        'syo_acao': ['syo_evento'],
        'syo_encaminhamento': ['syo_evento'],
        'syo_registrointerface': ['syo_evento'],
        'syo_historicoetapafunilevento': ['syo_evento'],
        'syo_evento_obs': ['syo_evento'],  # Evento_obs depende de evento
        'syo_clientealteracao': ['syo_cliente'],
        'syo_clientenovaclassificacao': ['syo_cliente'],
        'syo_empresacliente': ['syo_cliente'],
        'syo_donocontacliente': ['syo_cliente'],
        'syo_camposregistrointerface': ['syo_registrointerface'],
        'syo_campointerfacenegociacao': ['syo_interfacenegociacao']
    }
    
    # Configurar dependências específicas
    for dependent_table, prerequisite_tables in dependencies.items():
        if dependent_table in tasks:
            for prerequisite in prerequisite_tables:
                if prerequisite in tasks:
                    tasks[prerequisite] >> tasks[dependent_table]
    
    # ===== TASKS SILVER REMOVIDAS =====
    #
    # As seguintes tasks foram MOVIDAS para V3-SILVER-SYONET:
    # - task_oportunidades_base
    # - task_im_assinatura
    # - task_maquinas_semana_passada
    # - task_maquinas_atual
    #
    # 🎯 Esta DAG agora processa APENAS dados BRONZE (syo_* tables)
    
    # ===== TASK FINAL BRONZE =====

    # Task de finalização para indicar que todas as tabelas bronze foram processadas
    bronze_complete = DummyOperator(
        task_id='bronze_processing_complete',
        dag=dag,
        trigger_rule=TriggerRule.NONE_FAILED_MIN_ONE_SUCCESS
    )

    # Todas as tabelas syo_ devem terminar antes da finalização
    all_syo_tasks = list(tasks.values())

    for task in all_syo_tasks:
        task >> bronze_complete
    
    return dag

# Criar a DAG inteligente
smart_unified_dag = create_smart_unified_dag()

# Registrar no globals para o Airflow
globals()['V3-BRONZE-SYONET'] = smart_unified_dag

def log_fallback_metrics(table_name, attempted_method, successful_method, **context):
    """Log métricas de fallback para monitoramento"""
    dag_run = context.get('dag_run')
    if dag_run:
        print(f"📊 MÉTRICA FALLBACK: {table_name} | Tentativa: {attempted_method} | Sucesso: {successful_method} | DAG: {dag_run.dag_id}")